{"name": "@bika/ui-storybook", "private": true, "version": "1.9.0-alpha.14", "type": "module", "scripts": {"_dev": "vite", "dev": "dotenv -e ../web/.env.local -- storybook dev -p 6006", "_build": "tsc && vite build", "build": "NODE_OPTIONS=--max-old-space-size=10240  storybook build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "serve-storybook": "dotenv -e ../web/.env.local -- serve storybook-static"}, "dependencies": {"@bika/api-caller": "workspace:*", "@bika/contents": "workspace:*", "@bika/domains": "workspace:*", "@bika/types": "workspace:*", "@bika/ui": "workspace:*", "autoprefixer": "^10.4.16", "postcss": "^8.4.40", "react": "18.3.1", "react-dom": "18.3.1", "toolsdk": "workspace:*"}, "devDependencies": {"@chromatic-com/storybook": "^3", "@storybook/addon-essentials": "^8.4.4", "@storybook/addon-interactions": "^8.4.4", "@storybook/addon-links": "^8.4.4", "@storybook/addon-onboarding": "^8.4.4", "@storybook/addon-postcss": "^2.0.0", "@storybook/blocks": "^8.4.4", "@storybook/react": "^8.4.4", "@storybook/react-vite": "^8.4.4", "@storybook/test": "^8.4.4", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.14.0", "@typescript-eslint/parser": "^8.14.0", "@vitejs/plugin-react-swc": "^3.6.0", "eslint": "^8", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.5", "eslint-plugin-storybook": "^0.11.0", "storybook": "^8.4.4", "storybook-addon-root-attributes": "latest", "typescript": "^5.7.2", "vite": "^5.1.4"}}