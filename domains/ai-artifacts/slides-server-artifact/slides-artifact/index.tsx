import { Stack, Box, IconButton, Modal, ModalDialog } from '@mui/joy';
import { useTheme } from '@mui/joy/styles';
import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { useLocale } from '@bika/contents/i18n';
import { Slides, SlidesOutline } from '@bika/types/ai/vo';
import { Button } from '@bika/ui/button';
import ChevronLeftOutlined from '@bika/ui/icons/components/chevron_left_outlined';
import ChevronRightOutlined from '@bika/ui/icons/components/chevron_right_outlined';
import CloseOutlined from '@bika/ui/icons/components/close_outlined';
import ExpandOutlined from '@bika/ui/icons/components/expand_outlined';
import EyeOpenOutlined from '@bika/ui/icons/components/eye_open_outlined';
import ListOutlined from '@bika/ui/icons/components/list_outlined';
import CodeOutlined from '@bika/ui/icons/doc_hide_components/code_outlined';
import { Typography } from '@bika/ui/texts';
import { ToggleButtonGroup } from '@bika/ui/toggle-button-group';
import { ExpandedSlide } from './expanded';
import { useThrottle } from './hook';
import { SlideItem } from './slide';

// 引入单个幻灯片组件
interface SlidesArtifactProps {
  slides: Slides;
  outline: SlidesOutline;
}

export const SlidesArtifact = (props: SlidesArtifactProps) => {
  const theme = useTheme();
  const { t } = useLocale();
  const [viewMode, setViewMode] = useState<'preview' | 'code' | 'outline'>('preview');
  const [expandedSlide, setExpandedSlide] = useState<number | null>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const thumbnailContainerRef = useRef<HTMLDivElement>(null);

  // 使用节流处理 slides 数据 - 每1000ms最多更新一次
  const throttledSlides = useThrottle(props.slides, 1000);

  const outline = props.outline.outline;

  // 当PPT数量变化时，滚动到底部
  useEffect(() => {
    if (scrollContainerRef.current && viewMode !== 'outline') {
      scrollContainerRef.current.scrollTop = scrollContainerRef.current.scrollHeight;
    }
  }, [throttledSlides.length, viewMode]);

  const handleExpandSlide = useCallback((index: number) => {
    setExpandedSlide(index);
    // 滚动缩略图到当前选中项
    setTimeout(() => {
      if (thumbnailContainerRef.current) {
        const thumbnailElement = thumbnailContainerRef.current.querySelector(`[data-thumbnail-index="${index}"]`);
        if (thumbnailElement) {
          thumbnailElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });
        }
      }
    }, 100);
  }, []);

  const navigateSlide = useCallback(
    (direction: 'prev' | 'next') => {
      if (expandedSlide === null) return;
      const newIndex =
        direction === 'prev' ? Math.max(0, expandedSlide - 1) : Math.min(throttledSlides.length - 1, expandedSlide + 1);
      setExpandedSlide(newIndex);

      // 滚动缩略图到当前选中项
      setTimeout(() => {
        if (thumbnailContainerRef.current) {
          const thumbnailElement = thumbnailContainerRef.current.querySelector(`[data-thumbnail-index="${newIndex}"]`);
          if (thumbnailElement) {
            thumbnailElement.scrollIntoView({
              behavior: 'smooth',
              block: 'center',
            });
          }
        }
      }, 100);
    },
    [expandedSlide, throttledSlides.length],
  );

  const tabNavigation = useMemo(
    () => (
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
        <ToggleButtonGroup
          value={viewMode}
          onChange={(_event, newValue) => {
            if (newValue) {
              setViewMode(newValue as 'preview' | 'code' | 'outline');
            }
          }}
          sx={{
            backgroundColor: 'var(--bg-controls)',
            gap: 0,
            height: '32px',
            padding: '4px',
            borderRadius: '6px',
          }}
        >
          <Button
            value="preview"
            size="md"
            variant={viewMode === 'preview' ? 'fill' : 'outlined'}
            color={viewMode === 'preview' ? 'primary' : 'neutral'}
            startDecorator={<EyeOpenOutlined currentColor />}
            sx={{
              borderRadius: '6px',
              height: '24px',
              minHeight: '24px',
              border: 'none',
              backgroundColor: viewMode === 'preview' ? 'var(--brand) !important' : undefined,
              minWidth: '76px',
            }}
          >
            {t.ai.artifact_preview}
          </Button>
          <Button
            value="code"
            size="md"
            variant={viewMode === 'code' ? 'fill' : 'outlined'}
            color={viewMode === 'code' ? 'primary' : 'neutral'}
            startDecorator={<CodeOutlined currentColor />}
            sx={{
              borderRadius: '6px',
              height: '24px',
              minHeight: '24px',
              border: 'none',
              backgroundColor: viewMode === 'code' ? 'var(--brand) !important' : undefined,
              minWidth: '76px',
            }}
          >
            {t.ai.artifact_code}
          </Button>
          <Button
            value="outline"
            size="md"
            variant={viewMode === 'outline' ? 'fill' : 'outlined'}
            color={viewMode === 'outline' ? 'primary' : 'neutral'}
            startDecorator={<ListOutlined currentColor />}
            sx={{
              borderRadius: '6px',
              height: '24px',
              minHeight: '24px',
              border: 'none',
              backgroundColor: viewMode === 'outline' ? 'var(--brand) !important' : undefined,
              minWidth: '76px',
            }}
          >
            Outline
          </Button>
        </ToggleButtonGroup>

        {/* 右侧按钮区域 */}
        <Stack direction="row" spacing={1}>
          {/* 最大化按钮 */}
          <IconButton
            size="sm"
            variant="outlined"
            onClick={() => {
              setExpandedSlide(0);
            }}
            sx={{
              border: 'none',
              color: 'var(--text-primary)',
              '&:hover': { bgcolor: 'var(--bg-controls-hover)' },
            }}
          >
            <ExpandOutlined />
          </IconButton>
        </Stack>
      </Stack>
    ),
    [viewMode, t.ai.artifact_preview, t.ai.artifact_code],
  );

  return (
    <Stack spacing={2} sx={{ height: '100%', flex: 1 }}>
      {/* Tab Navigation */}
      {tabNavigation}

      {/* Main Content */}
      <Box
        sx={{
          flex: 1,
          height: 'calc(100% - 64px)',
          overflow: 'hidden',
          borderRadius: viewMode === 'outline' ? '8px' : '0px',
          bgcolor: viewMode === 'outline' ? 'var(--bg-surface)' : 'transparent',
          border: viewMode === 'outline' ? '1px solid var(--border-default)' : 'none',
        }}
      >
        {viewMode === 'outline' && (
          <Box
            sx={{
              height: '100%',
              overflow: 'hidden',
              bgcolor: 'var(--bg-controls)',
              border: '1px solid var(--border-default)',
              borderRadius: '8px',
            }}
          >
            {/* Title Section */}
            <Box
              sx={{
                p: 1,
                textAlign: 'center',
                borderBottom: '1px solid var(--border-default)',
              }}
            >
              <Typography level="h7" sx={{ color: 'var(--text-primary)' }}>
                {outline.title}
              </Typography>
            </Box>

            {/* Content Section */}
            <Box sx={{ p: 1, height: 'calc(100% - 80px)', overflow: 'auto' }}>
              <Stack spacing={2}>
                {outline.slides.map((slide, index) => (
                  <Box
                    key={index}
                    sx={{
                      border: '1px solid var(--border-default)',
                      borderRadius: '8px',
                      p: 2,
                      cursor: 'pointer',
                      bgcolor: 'var(--bg-controls)',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        bgcolor: 'var(--bg-controls-hover)',
                        borderColor: 'var(--brand)',
                      },
                    }}
                    onClick={() => setViewMode('preview')}
                  >
                    <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
                      <Typography level="h7" sx={{ color: 'var(--text-primary)', fontWeight: 500 }}>
                        {slide.slide_number}. {slide.slide_title}
                      </Typography>
                      <Box
                        sx={{
                          fontSize: '12px',
                          bgcolor: 'var(--bgTagDefault)',
                          px: 1.5,
                          py: 0.5,
                          borderRadius: '4px',
                          color: 'var(--static)',
                        }}
                      >
                        {slide.slide_type}
                      </Box>
                    </Stack>
                    <Typography level="b3" sx={{ color: 'var(--text-secondary)', lineHeight: 1.5 }}>
                      {slide.description}
                    </Typography>
                  </Box>
                ))}
              </Stack>
            </Box>
          </Box>
        )}

        {(viewMode === 'preview' || viewMode === 'code') && (
          <Box
            ref={scrollContainerRef}
            sx={{ height: '100%', overflow: 'auto', p: viewMode === 'preview' ? 0 : 2, bgcolor: 'transparent' }}
          >
            {/* Slides Grid */}
            <Stack spacing={3}>
              {throttledSlides.map((slide, index) => (
                <SlideItem
                  key={`${slide.slide_number}-${index}`}
                  slide={slide}
                  index={index}
                  slideViewMode={viewMode as 'preview' | 'code'}
                  theme={theme}
                  onExpandSlide={handleExpandSlide}
                />
              ))}
            </Stack>
          </Box>
        )}
      </Box>

      {/* Expanded Slide Modal */}
      <Modal open={expandedSlide !== null} onClose={() => setExpandedSlide(null)}>
        <ModalDialog
          sx={{
            width: '90vw',
            height: '90vh',
            maxWidth: 'none',
            maxHeight: 'none',
            p: 0,
            bgcolor: 'var(--bg-surface)',
            border: '1px solid var(--border-default)',
          }}
        >
          {/* Modal Header */}
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            sx={{ p: 2, borderBottom: '1px solid var(--border-default)' }}
          >
            <Typography level="h4" sx={{ color: 'var(--text-primary)', fontWeight: 500 }}>
              {expandedSlide !== null &&
                throttledSlides[expandedSlide] &&
                `${throttledSlides[expandedSlide].slide_number}. ${throttledSlides[expandedSlide].title}`}
            </Typography>
            <Stack direction="row" spacing={1} alignItems="center">
              <IconButton
                size="sm"
                variant="outlined"
                onClick={() => navigateSlide('prev')}
                disabled={expandedSlide === 0}
                sx={{
                  bgcolor: 'var(--bg-controls)',
                  borderColor: 'var(--border-default)',
                  color: 'var(--text-primary)',
                  '&:hover': { bgcolor: 'var(--bg-controls-hover)' },
                }}
              >
                <ChevronLeftOutlined />
              </IconButton>
              <Typography level="body-sm" sx={{ color: 'var(--text-secondary)' }}>
                {expandedSlide !== null ? expandedSlide + 1 : 0} / {throttledSlides.length}
              </Typography>
              <IconButton
                size="sm"
                variant="outlined"
                onClick={() => navigateSlide('next')}
                disabled={expandedSlide === throttledSlides.length - 1}
                sx={{
                  bgcolor: 'var(--bg-controls)',
                  borderColor: 'var(--border-default)',
                  color: 'var(--text-primary)',
                  '&:hover': { bgcolor: 'var(--bg-controls-hover)' },
                }}
              >
                <ChevronRightOutlined />
              </IconButton>
              <IconButton
                size="sm"
                variant="outlined"
                onClick={() => setExpandedSlide(null)}
                sx={{
                  bgcolor: 'var(--bg-controls)',
                  borderColor: 'var(--border-default)',
                  color: 'var(--text-primary)',
                  '&:hover': { bgcolor: 'var(--bg-controls-hover)' },
                }}
              >
                <CloseOutlined />
              </IconButton>
            </Stack>
          </Stack>

          {/* Modal Content - 左右分栏布局 */}
          <Box sx={{ flex: 1, height: 'calc(100% - 64px)', overflow: 'hidden', display: 'flex' }}>
            {/* 左侧缩略图列表 */}
            <Box
              ref={thumbnailContainerRef}
              sx={{
                width: '260px',
                height: '100%',
                borderRight: '1px solid var(--border-default)',
                bgcolor: 'var(--bg-controls)',
                overflow: 'auto',
                p: 2,
              }}
            >
              <Typography
                level="body-sm"
                sx={{
                  color: 'var(--text-secondary)',
                  mb: 2,
                  fontWeight: 500,
                  textAlign: 'center',
                }}
              >
                Slides ({throttledSlides.length})
              </Typography>
              <Stack spacing={2}>
                {throttledSlides.map((slide, index) => {
                  const thumbnailContent = `
                    <!DOCTYPE html>
                    <html data-theme="${theme.palette.mode}">
                      <head>
                        <style>
                          :root { color-scheme: ${theme.palette.mode}; }
                          html, body {
                            margin: 0;
                            padding: 0;
                            width: 100%;
                            height: 100%;
                            background: var(--bg-surface);
                            color: var(--text-primary);
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                            font-size: 8px;
                            line-height: 1.2;
                            overflow: hidden;
                          }
                          * {
                            font-size: 8px !important;
                            margin: 0 !important;
                            padding: 2px !important;
                          }
                          h1, h2, h3, h4, h5, h6 {
                            font-size: 10px !important;
                            margin: 2px 0 !important;
                            padding: 2px !important;
                          }
                        </style>
                      </head>
                      <body>${slide.html_content}</body>
                    </html>
                  `;

                  return (
                    <Box
                      key={`thumbnail-${slide.slide_number}-${index}`}
                      data-thumbnail-index={index}
                      sx={{
                        display: 'flex',
                        border: expandedSlide === index ? '2px solid var(--brand)' : '1px solid var(--border-default)',
                        borderRadius: '6px',
                        overflow: 'hidden',
                        cursor: 'pointer',
                        bgcolor: 'var(--bg-surface)',
                        transition: 'all 0.2s ease',
                        boxShadow: expandedSlide === index ? '0 2px 8px rgba(0,0,0,0.15)' : '0 1px 3px rgba(0,0,0,0.1)',
                        height: '120px',
                        '&:hover': {
                          borderColor: 'var(--brand)',
                          transform: 'translateY(-1px)',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                        },
                      }}
                      onClick={() => setExpandedSlide(index)}
                    >
                      {/* 左侧数字编号 */}
                      <Box
                        sx={{
                          width: '32px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          bgcolor: expandedSlide === index ? 'var(--brand)' : 'var(--bg-elevated)',
                          borderRight: '1px solid var(--border-default)',
                        }}
                      >
                        <Typography
                          level="body-sm"
                          sx={{
                            color: expandedSlide === index ? 'var(--on-brand)' : 'var(--text-primary)',
                            fontSize: '11px',
                            fontWeight: 600,
                            fontFamily: 'monospace',
                          }}
                        >
                          {String(index + 1).padStart(2, '0')}
                        </Typography>
                      </Box>

                      {/* 右侧缩略图内容 */}
                      <Box
                        sx={{
                          flex: 1,
                          height: '100%',
                          overflow: 'hidden',
                          position: 'relative',
                          bgcolor: 'var(--bg-surface)',
                        }}
                      >
                        <iframe
                          srcDoc={thumbnailContent}
                          width="100%"
                          height="100%"
                          style={{
                            border: 'none',
                            pointerEvents: 'none',
                            transform: 'scale(1)',
                            transformOrigin: 'center center',
                            objectFit: 'cover',
                          }}
                        />
                        {/* 当前选中的指示器 */}
                        {expandedSlide === index && (
                          <Box
                            sx={{
                              position: 'absolute',
                              top: 4,
                              right: 4,
                              width: 6,
                              height: 6,
                              borderRadius: '50%',
                              bgcolor: 'var(--brand)',
                              boxShadow: '0 0 0 1px var(--bg-surface)',
                            }}
                          />
                        )}
                      </Box>
                    </Box>
                  );
                })}
              </Stack>
            </Box>

            {/* 右侧主要预览区域 */}
            <Box sx={{ flex: 1, height: '100%', overflow: 'hidden' }}>
              {expandedSlide !== null && throttledSlides[expandedSlide] && (
                <ExpandedSlide slide={throttledSlides[expandedSlide]} theme={theme} />
              )}
            </Box>
          </Box>
        </ModalDialog>
      </Modal>
    </Stack>
  );
};
