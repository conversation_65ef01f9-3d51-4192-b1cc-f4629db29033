import { Box } from '@mui/joy';
import dynamic from 'next/dynamic';
import React, { useMemo } from 'react';
import { Slide } from '@bika/types/ai/vo';
import { useThrottle } from './hook';

const CodeViewWithHeader = dynamic(() => import('@bika/ui/code-view').then((module) => module.CodeViewWithHeader), {
  loading: () => <>loading...</>,
  ssr: false,
});

interface SlideItemProps {
  slide: Slide;
  index: number;
  slideViewMode: 'preview' | 'code';
  theme: { palette: { mode: string } };
  onExpandSlide: (index: number) => void;
}

export const SlideItem = React.memo(({ index, slide, slideViewMode, theme, onExpandSlide }: SlideItemProps) => {
  // 对 html_content 进行节流处理 - 每1000ms最多更新一次
  const throttledHtmlContent = useThrottle(slide.html_content, 1000);

  const iframeContent = useMemo(
    () => `
    <!DOCTYPE html>
    <html data-theme="${theme.palette.mode}">
      <head>
        <style>
          :root { color-scheme: ${theme.palette.mode}; }
          body {
            margin: 0;
            padding: ${slideViewMode === 'preview' ? '0' : '20px'};
            background: var(--bg-surface);
            color: var(--text-primary);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: ${slideViewMode === 'preview' ? 'hidden' : 'auto'};
            width: 100%;
            height: 100%;
            box-sizing: border-box;
          }
          ${
            slideViewMode === 'preview'
              ? `
          * {
            max-width: 100% !important;
            box-sizing: border-box !important;
          }
          `
              : ''
          }
        </style>
      </head>
      <body>${throttledHtmlContent}</body>
    </html>
  `,
    [throttledHtmlContent, theme.palette.mode, slideViewMode],
  );

  return (
    <Box
      sx={{
        // preview 模式和 code 模式都显示 border 和圆角
        border: '1px solid var(--border-default)',
        borderRadius: '8px',
        overflow: 'hidden',
        ...(slideViewMode === 'preview'
          ? {
              // preview 模式的特定样式
            }
          : {
              bgcolor: 'var(--bg-controls)',
            }),
      }}
    >
      {/* Slide Content */}
      <Box sx={{ height: slideViewMode === 'preview' ? 'auto' : '400px', overflow: 'hidden' }}>
        {slideViewMode === 'preview' ? (
          <Box
            sx={{
              minHeight: '300px',
              cursor: 'pointer',
              position: 'relative',
              '&:hover::after': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                bgcolor: 'rgba(0, 0, 0, 0.1)',
              },
            }}
            onClick={() => onExpandSlide(index)}
          >
            <iframe srcDoc={iframeContent} width="100%" height="100%" style={{ border: 'none', minHeight: '300px' }} />
          </Box>
        ) : (
          <Box sx={{ height: '100%', overflow: 'hidden' }}>
            <CodeViewWithHeader
              title={`第 ${slide.slide_number} 页`}
              lang="html"
              // theme={theme.palette.mode === 'dark' ? 'catppuccin-mocha' : 'catppuccin-latte'}
              code={throttledHtmlContent}
            />
          </Box>
        )}
      </Box>
    </Box>
  );
});

SlideItem.displayName = 'SlideItem';
