'use client';

import { useChat } from '@ai-sdk/react';
import type { Message as AISDKMessage, ToolInvocation, ToolInvocationUIPart } from '@ai-sdk/ui-utils';
import { motion } from 'framer-motion';
import React, { useCallback, useEffect } from 'react';
import { AISkillsetClientRegistry } from '@bika/domains/ai-skillset/client-registry';
import type { AIMessageAnnotation, AIMessageBO, AIIntentParams } from '@bika/types/ai/bo';
import type { AIResolveVO, AIChatOption } from '@bika/types/ai/vo';
import { AIToolExecutionErrorSchema } from '@bika/types/ai/vo';
import type { Locale } from '@bika/types/i18n/bo';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { useGlobalState, useGlobalContext } from '@bika/types/website/context';
import { Stack, Box } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/texts';
import { type AIChatInputOptions, type AIChatInputRefHandle, AIChatInput } from './ai-chat-input';
import { AIChatMessage } from './ai-chat-message';
import { AIChatPrompts } from './ai-chat-prompts';
import { AIMessageAnnotations } from './ai-message-annotations';
// import { MessageIcon } from './ai-message-icon';
import { AIChatArtifact } from './artifacts/ai-artifact';
import { CreditLimit } from './credit-limit';
import { useInputStore } from './input-store';
import { useChatScrollToBottom } from './use-chat-scroll-to-bottom';
import { Message } from '../wizard/message';
import { useAutoResume } from './hooks/use-auto-resume';
import { TOOL_RESULT_CANCELED } from './tools/type';

type Props = AIChatInputOptions & {
  chatId: string;
  api: '/api/ai/admin' | '/api/ai/chat' | '/api/ai/replay';
  // 可以发送 options 给对应接口给予不同行为，该 option 可供用户选择
  options?: AIChatOption[];
  customBody?: {
    forceLocale?: Locale;
    initAIIntent?: AIIntentParams;
    wizardId?: string;
    messageIndex?: number;
  };
  initialMessages?: AISDKMessage[];
  disabled?: boolean;
  onFinish?: (message: AISDKMessage) => void;
  // You can control the artifact whether in RIGHT_SIDE or as UI MODAL
  // 显示模式，窄边 or 独立大窗 or 模态窗
  displayMode?: 'COPILOT' | 'MODAL' | 'VIEW';
  hideInput?: boolean;
  autoResume?: boolean;
};

export interface AIChatHandle {
  doAppendMessage: (msg: string) => void;
}

/**
 *
 * Pure AI SDK useChat UI.  DONT DONT DONT use this with trpc!!!
 *
 * @param props
 * @param ref
 * @returns
 */
function InternalAIChatUIRenderer(props: Props, ref: React.Ref<AIChatHandle>) {
  const { chatId, autoResume = true } = props;
  const isCopilot = props.displayMode === 'COPILOT';
  const { containerRef, onViewportLeave, onViewportEnter, scrollToBottom, isAtBottom } = useChatScrollToBottom();
  const inputRef = React.useRef<AIChatInputRefHandle>(null);
  const { attachments, clearAttachments } = useInputStore();

  const experimentalAttachments = React.useMemo(
    () =>
      attachments.map((item) => ({
        name: item.name,
        contentType: item.contentType,
        url: `${item.url}?id=${item.id}`,
      })),
    [attachments],
  );

  // 使用 ref 来保存最新的 experimentalAttachments 值
  const experimentalAttachmentsRef = React.useRef(experimentalAttachments);

  // 每次 experimentalAttachments 更新时，同步更新 ref
  React.useEffect(() => {
    experimentalAttachmentsRef.current = experimentalAttachments;
  }, [experimentalAttachments]);

  const {
    setMessages,
    messages,
    error,
    status,
    append,
    input,
    setInput,
    data: chatData,
    handleInputChange,
    handleSubmit,
    addToolResult,
    // eslint-disable-next-line camelcase
    experimental_resume: goResume,
    stop,
  } = useChat({
    id: chatId,
    api: props.api,
    initialMessages: props.initialMessages,
    maxSteps: 5,
    experimental_throttle: 400,
    sendExtraMessageFields: true,
    onFinish: props.onFinish,
    onToolCall: async ({ toolCall }) => {
      const toolName = toolCall.toolName;
      const toolCallCargs = toolCall.args;
      const toolCallId = toolCall.toolCallId;
      console.log('TODO Client Tool Call:', toolName, toolCallCargs, toolCallId);
    },
    generateId: () => chatId,
    experimental_prepareRequestBody: (body) => {
      const lastMessage = body.messages[body.messages.length - 1];
      const option = (body.requestBody as { option?: string })?.option || inputRef.current?.option;

      // 使用 ref 获取最新的 experimentalAttachments 值
      const currentAttachments = experimentalAttachmentsRef.current;

      return {
        id: body.id,
        lastMessage,
        option,
        experimental_attachments: currentAttachments,
        ...props.customBody,
      };
    },
  });
  console.log('🚀 ~ InternalAIChatUIRenderer ~ messages:', messages);

  useAutoResume({
    autoResume,
    initialMessages: props.initialMessages || [],
    // eslint-disable-next-line camelcase
    goResume,
    data: chatData,
    setMessages,
  });

  let parsedCallToolErrors: Record<string, string> | undefined;

  useEffect(() => {
    if (error?.message && typeof error?.message === 'string') {
      console.error('error.message', error?.message);
      try {
        const parsedResult = AIToolExecutionErrorSchema.safeParse(JSON.parse(error?.message));
        if (parsedResult.success) {
          parsedCallToolErrors = parsedResult.data.errors;
          // 如果当前的tool call part 还没有返回（error 先触发，但是messages还未更新），则等待1s, 等message 更新再setToolResult
          if (
            messages.some((m) =>
              m.parts?.some(
                (t) => t.type === 'tool-invocation' && t.toolInvocation.toolCallId === parsedResult.data.toolCallId,
              ),
            )
          ) {
            console.log('🚀 ~ addToolResult ~ :', parsedResult.data.toolCallId);
            addToolResult({
              toolCallId: parsedResult.data.toolCallId,
              result: {
                isError: true,
                error: {
                  message: parsedResult.data.message,
                },
              },
            });
          }
        }
      } catch (e) {
        console.error('error AIToolExecutionErrorSchema.safeParse', e);
      }
    }
  }, [error?.message, messages, addToolResult]);

  const isCreditError = React.useMemo(() => {
    try {
      const errorObj = JSON.parse(error?.message || '{}');
      return errorObj._error.code === 10001;
    } catch {
      return false;
    }
  }, [error]);

  // const skillsetSelects: SkillsetSelectDTO[] = React.useMemo(() => {
  //   // 打开一个对话历史，对话中调用了 tool，但由于这是历史数据，tool 定义的 execute 方法还没执行，因此 chatData 还是 undefined 状态
  //   // 这会导致这个 tool 对应的 ui 无法渲染
  //   if (!chatData)
  //     return [
  //       { kind: 'preset', key: 'debug' },
  //       { kind: 'preset', key: 'default' },
  //       { kind: 'preset', key: 'bika-ai-page' },
  //       { kind: 'preset', key: 'bika-search' },
  //       { kind: 'preset', key: 'bika-space' },
  //       { kind: 'preset', key: 'bika-media' },
  //       { kind: 'preset', key: 'bika-database' },
  //     ];
  //   return parseSkillsetSelectsFromChatData(chatData);
  // }, [chatData]);

  const globalContext = useGlobalContext();
  // 自动将最新收到的 tool，设置成artifact 吗？
  const [autoRollArtifact, setAutoRollArtifact] = React.useState<boolean>(true);
  const [artifact, setArtifact] = React.useState<
    { message: AISDKMessage; tool: ToolInvocation; skillsets: SkillsetSelectDTO[] } | undefined
  >(undefined);

  const [_globalAritfact, setGlobalArtifact] = useGlobalState<
    | {
        message: AISDKMessage;
        tool: ToolInvocation;
        // 用到了什么 skillsets UI 组件
        skillsets: SkillsetSelectDTO[];
      }
    | undefined
  >('AI_ARTIFACT');

  useEffect(() => {
    if (chatId) {
      setTimeout(() => {
        scrollToBottom('instant');
      }, 1000);
    }
  }, [chatId]);

  const lastMessage = React.useMemo(() => {
    if (messages.length === 0) return undefined;

    const newLastMessage = messages[messages.length - 1];
    return newLastMessage;
  }, [messages]);

  const isLastMessageCall = React.useMemo(() => {
    if (!lastMessage || !lastMessage.toolInvocations) return false;
    return lastMessage.toolInvocations.some((toolInvocation) => toolInvocation.state === 'call');
  }, [lastMessage]);

  const lastMsgSkillsetSelects: SkillsetSelectDTO[] = React.useMemo(() => {
    // 打开一个对话历史，对话中调用了 tool，但由于这是历史数据，tool 定义的 execute 方法还没执行，因此 chatData 还是 undefined 状态
    // 这会导致这个 tool 对应的 ui 无法渲染
    if (!lastMessage)
      return [
        { kind: 'preset', key: 'debug' },
        { kind: 'preset', key: 'default' },
        { kind: 'preset', key: 'bika-ai-page' },
        { kind: 'preset', key: 'bika-search' },
        { kind: 'preset', key: 'bika-space' },
        { kind: 'preset', key: 'bika-media' },
        { kind: 'preset', key: 'bika-database' },
      ];
    return AIMessageAnnotations.parseSkillsets(lastMessage.annotations as AIMessageAnnotation[] | undefined);
    // }, [chatData]);
  }, [lastMessage]);

  // 检查是否有工具需要审批
  const hasToolsPendingApproval = React.useMemo(() => {
    if (!lastMessage || !lastMessage.toolInvocations) return false;
    return lastMessage.toolInvocations.some((toolInvocation) => {
      // 只检查处于 call 状态的工具调用
      if (toolInvocation.state !== 'call') return false;

      // 检查是否需要审批
      const needApproval = lastMsgSkillsetSelects?.some(
        (skillset) => 'needApprovals' in skillset && skillset.needApprovals?.includes(toolInvocation.toolName),
      );

      return needApproval;
    });
  }, [lastMessage, lastMsgSkillsetSelects]);

  const emptyAssistantMessage = useCallback((message: AISDKMessage) => {
    if (message.role !== 'assistant') return false;
    const hasContent =
      message.content ||
      message.parts?.some((part) => (part.type === 'text' && part.text) || part.type === 'tool-invocation');
    return !hasContent;
  }, []);

  // 用于控制 loading 消息的显示，避免在新消息到达前出现空白间隙
  const showLoadingMessage = React.useMemo(() => {
    // 当状态为 submitted 时显示 loading
    if (status === 'submitted') {
      return true;
    }

    // 当状态为 streaming 时，检查是否有助手消息内容
    if (status === 'streaming' && lastMessage) {
      // 如果助手消息还没有内容，继续显示 loading
      return emptyAssistantMessage(lastMessage);
    }

    return false;
  }, [status, lastMessage, emptyAssistantMessage]);

  // 提交消息后，自动滚动到最底部
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (status === 'submitted' || status === 'ready' || status === 'error') {
      setTimeout(() => {
        scrollToBottom('smooth');
      }, 100);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [status]);

  useEffect(() => {
    if (status === 'streaming') {
      let rafId: number;

      const scroll = () => {
        scrollToBottom('instant');
        rafId = requestAnimationFrame(scroll);
      };

      scroll();

      return () => cancelAnimationFrame(rafId);
    }
  }, [status, scrollToBottom]);

  // 获取最后一个 tool-invocation 的部分，判断值被变化，用于自动滚动 artifact 的出现
  const lastTool: ToolInvocationUIPart | undefined = React.useMemo(() => {
    if (!lastMessage) return undefined;
    const toolParts = lastMessage.parts.filter((p) => p.type === 'tool-invocation');
    if (toolParts.length === 0) return undefined;
    return toolParts[toolParts.length - 1];
  }, [lastMessage]);

  // Create a stop event handler that intercepts chat stop events
  const handleStop = React.useCallback(() => {
    // If there are tools pending approval, cancel them
    if (hasToolsPendingApproval && lastMessage?.toolInvocations) {
      const pendingTools = lastMessage.toolInvocations.filter((toolInvocation) => {
        if (toolInvocation.state !== 'call') return false;

        const needApproval = lastMsgSkillsetSelects?.some(
          (skillset) => 'needApprovals' in skillset && skillset.needApprovals?.includes(toolInvocation.toolName),
        );

        return needApproval;
      });

      // Cancel all pending approval tools
      for (const toolInvocation of pendingTools) {
        try {
          console.info('Canceling tool pending approval:', toolInvocation);
          addToolResult({
            toolCallId: toolInvocation.toolCallId,
            result: TOOL_RESULT_CANCELED,
          });
        } catch (err) {
          console.error('Failed to cancel tool execution:', err);
        }
      }
    }

    // Find the last tool that was being executed (in 'call' state)
    // Only cancel if there's a tool currently being executed
    if (lastTool?.toolInvocation?.state === 'call' && lastTool.toolInvocation.toolCallId) {
      try {
        console.info('addToolResult', lastTool);
        // Cancel the last tool execution by adding TOOL_RESULT_CANCELED
        addToolResult({
          toolCallId: lastTool.toolInvocation.toolCallId,
          result: TOOL_RESULT_CANCELED,
        });
        console.log('Tool execution canceled:', lastTool.toolInvocation.toolCallId);
      } catch (err) {
        console.error('Failed to cancel tool execution:', err);
      }
    }
    stop();
  }, [stop, addToolResult, lastTool, hasToolsPendingApproval, lastMessage, lastMsgSkillsetSelects]);

  const messageIndex = props.customBody?.messageIndex;

  useEffect(() => {
    if (!props.initialMessages?.some((msg) => msg.id === artifact?.message.id)) {
      setArtifact(undefined);
    }
    setTimeout(() => {
      scrollToBottom('smooth');
    }, 100);
  }, [messageIndex]);

  // 使用单独的effect来处理artifact更新
  // 当 Last Tool 发生变化，设置该 tool 为 artifact
  useEffect(() => {
    const isCalling = lastTool?.toolInvocation.state === 'call';
    const isJustUpdated = artifact?.tool.state === 'call' && lastTool?.toolInvocation.state === 'result';
    if (
      props.displayMode !== 'COPILOT' &&
      autoRollArtifact === true &&
      lastTool &&
      lastTool.type === 'tool-invocation' &&
      lastTool.toolInvocation &&
      (isCalling || isJustUpdated) &&
      lastMessage
    ) {
      const skillsetUIMap = AISkillsetClientRegistry.getManySkillsetUI(lastMsgSkillsetSelects);
      const toolUIConfig = skillsetUIMap[lastTool.toolInvocation.toolName];
      // 必须是强制有 artifact (server-artifact 或者 component)的才会 auto roll
      if (toolUIConfig?.artifact) {
        setArtifact({ message: lastMessage, tool: lastTool.toolInvocation, skillsets: lastMsgSkillsetSelects });
      }
    }
  }, [lastTool, autoRollArtifact, lastMessage, props.displayMode, lastMsgSkillsetSelects]);

  const doAppendMessage = React.useCallback(
    (msg: string) => {
      append(
        {
          role: 'user',
          content: msg,
        },
        {
          body: {
            ...props.customBody,
            option: inputRef.current?.option,
          },
          experimental_attachments: experimentalAttachments,
        },
      );
      // 确保在 append 调用后再清空
      setTimeout(() => {
        clearAttachments();
      }, 0);
    },
    [append, experimentalAttachments, clearAttachments, props.customBody],
  );

  React.useImperativeHandle(ref, () => ({
    doAppendMessage,
  }));

  const disabled = React.useMemo(
    () => props.disabled === true || (status !== 'ready' && status !== 'error'),
    [props.disabled, status],
  );

  const isArtifactShow = !isCopilot && !!artifact;

  const initAIIntent = props.customBody?.initAIIntent;

  return (
    <Stack
      sx={{
        flex: 1,
        display: 'flex',
        height: '100%',
        paddingX: isCopilot ? '16px' : '24px',
      }}
    >
      <Box
        // ref={ref}
        sx={{
          display: 'flex',
          width: '100%',
          alignItems: 'flex-start',
          flexDirection: isArtifactShow ? 'row' : 'column',
          overflowY: isCopilot ? 'auto' : 'hidden',
          flex: 1,
        }}
        ref={containerRef}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
            width: isArtifactShow ? '45%' : '100%',
            alignItems: 'center',
          }}
        >
          <Box
            sx={{
              minWidth: '300px',
              display: 'flex',
              width: '100%',
              flexDirection: 'column',
              alignItems: 'center',
              overflowY: isCopilot ? 'none' : 'auto',
              overflowX: 'hidden',
              p: '24px 0 8px 0',
              flex: 1,
            }}
          >
            {messages
              .filter((m) => !emptyAssistantMessage(m))
              .map((m, idx) => (
                <AIChatMessage
                  chatId={chatId}
                  skillsets={lastMsgSkillsetSelects}
                  status={status}
                  key={idx}
                  message={m as AIMessageBO}
                  artifactToolId={artifact?.tool.toolCallId}
                  toolExecuteErrors={parsedCallToolErrors}
                  disabled={disabled || idx !== messages.length - 1} // 除了最后 1 个UI，其它全部disabled，不能交互
                  addToolResult={addToolResult}
                  initAIIntent={initAIIntent}
                  sendUI={async (res) => {
                    const resolve: AIResolveVO = {
                      type: 'UI',
                      uiResolve: res,
                    };
                    const msg = `/resolve:${JSON.stringify(resolve)}`;
                    doAppendMessage(msg);
                  }}
                  onSelectTool={(selectToolInvo, selectMsg) => {
                    // if COPILOT, set Global Artifact, and show UI Modal
                    if (props.displayMode === 'COPILOT') {
                      setGlobalArtifact({
                        message: selectMsg,
                        tool: selectToolInvo,
                        skillsets: lastMsgSkillsetSelects ?? [],
                      });
                      globalContext.showUIModal({ name: 'AI_ARTIFACT' });
                    } else {
                      setArtifact({ message: selectMsg, tool: selectToolInvo, skillsets: lastMsgSkillsetSelects });
                    }

                    // 如果选择了最后一个 tool，激活自动 artifact 弹现
                    if (lastTool?.toolInvocation === selectToolInvo) {
                      setAutoRollArtifact(true);
                    } else {
                      // 如果选择了不是最后一个 tool，那么就不需要自动滚动了
                      setAutoRollArtifact(false);
                    }
                  }}
                  sendMessage={async (text) => {
                    doAppendMessage(text);
                  }}
                />
              ))}
            {error && !isCreditError && parsedCallToolErrors == null && (
              <Typography textColor="var(--status-danger)">{error.message}</Typography>
            )}
            {isCreditError && <CreditLimit />}
            {showLoadingMessage && (
              <Stack key="loading" display="flex" flexDirection="row" sx={{ width: '100%', maxWidth: '720px' }}>
                {/* <MessageIcon type="assistant" initAIIntent={initAIIntent} /> */}
                <Stack
                  sx={{
                    backgroundColor: 'var(--bg-controls)',
                    fontSize: '16px',
                    borderRadius: '8px',
                    py: 2,
                    px: 1,
                    padding: '8px 16px 8px 16px',
                  }}
                >
                  <Message loading />
                </Stack>
              </Stack>
            )}
            <motion.div
              id="last-message-bottom"
              className="shrink-0 min-w-[24px] min-h-[4px]"
              onViewportLeave={onViewportLeave}
              onViewportEnter={onViewportEnter}
            />
          </Box>
          {props.hideInput !== true && (
            <>
              {lastMessage && !isCopilot && (
                <Box px={2}>
                  <AIChatPrompts
                    onClickPrompt={(userPrompt) => {
                      doAppendMessage(userPrompt);
                    }}
                    messageAnnotations={lastMessage.annotations as AIMessageAnnotation[] | undefined}
                  />
                </Box>
              )}
              {!isCopilot && (
                <AIChatInput
                  ref={inputRef}
                  status={hasToolsPendingApproval ? 'streaming' : status}
                  stop={handleStop}
                  isAtBottom={isAtBottom || status === 'streaming'}
                  options={props.options}
                  allowContextMenu={props.allowContextMenu}
                  context={props.context}
                  input={input}
                  setInput={setInput}
                  onChange={handleInputChange}
                  disabled={isLastMessageCall || hasToolsPendingApproval}
                  handleSubmit={(e) => {
                    handleSubmit(e, {
                      body: {
                        ...props.customBody,
                        option: inputRef.current?.option,
                      },
                      experimental_attachments: experimentalAttachments,
                      allowEmptySubmit: true,
                    });
                    // 使用 setTimeout 确保在 handleSubmit 完成后再清空
                    setTimeout(() => {
                      clearAttachments();
                    }, 0);
                  }}
                />
              )}
            </>
          )}
        </Box>

        {/* On Click Tool 的时候， 显示 Artifact, Copilot模式下，不会显示 artifact (auto roll)，除非手工点击 */}
        {artifact && !isCopilot && (
          <AIChatArtifact
            skillsets={artifact.skillsets}
            message={artifact.message}
            tool={artifact.tool}
            onClickClose={() => {
              setArtifact(undefined);
              setGlobalArtifact(undefined);
              setAutoRollArtifact(false);
            }}
            sx={{
              width: 'calc(55% - 24px)',
              flex: 'none',
              ml: 3,
              my: 3,
              animation: 'slideIn 0.3s ease-out',
              '@keyframes slideIn': {
                '0%': {
                  opacity: 0,
                  transform: 'translateX(20px)',
                  width: '0%',
                },
                '100%': {
                  opacity: 1,
                  transform: 'translateX(0)',
                  width: '45%',
                },
              },
            }}
          />
        )}
      </Box>
      {props.hideInput !== true && (
        <>
          {/* 这是个 Copilot 使用的组件 */}
          {lastMessage && isCopilot && (
            <Box px={2}>
              <AIChatPrompts
                onClickPrompt={(userPrompt) => {
                  doAppendMessage(userPrompt);
                }}
                messageAnnotations={lastMessage.annotations as AIMessageAnnotation[] | undefined}
              />
            </Box>
          )}
          {isCopilot && (
            <div className="flex justify-center items-center w-full">
              <AIChatInput
                ref={inputRef}
                status={hasToolsPendingApproval ? 'streaming' : status}
                stop={handleStop}
                isAtBottom={isAtBottom || status === 'streaming'}
                options={props.options}
                context={props.context}
                allowContextMenu={props.allowContextMenu}
                input={input}
                setInput={setInput}
                onChange={handleInputChange}
                disabled={isLastMessageCall || hasToolsPendingApproval}
                handleSubmit={(e) => {
                  handleSubmit(e, {
                    body: {
                      ...props.customBody,
                      option: inputRef.current?.option,
                    },
                    experimental_attachments: experimentalAttachments,
                    allowEmptySubmit: true,
                  });
                  // 使用 setTimeout 确保在 handleSubmit 完成后再清空
                  setTimeout(() => {
                    clearAttachments();
                  }, 0);
                }}
              />
            </div>
          )}
        </>
      )}
    </Stack>
  );
}
export const AIChatUIRenderer = React.memo(React.forwardRef(InternalAIChatUIRenderer));
