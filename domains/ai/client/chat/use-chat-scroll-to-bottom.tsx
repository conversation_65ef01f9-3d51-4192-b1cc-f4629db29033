import { useRef, useState, useCallback } from 'react';

type ScrollBehavior = 'auto' | 'instant' | 'smooth';

export function useChatScrollToBottom() {
  const containerRef = useRef<HTMLDivElement>(null);

  const [isAtBottom, setIsAtBottom] = useState(false);

  const scrollToBottom = useCallback((behavior: ScrollBehavior = 'smooth') => {
    const bottomElement = document.querySelector('#last-message-bottom');
    bottomElement?.scrollIntoView({ behavior, block: 'end' });
  }, []);

  const onViewportEnter = useCallback(() => {
    setIsAtBottom(true);
  }, []);

  const onViewportLeave = useCallback(() => {
    setIsAtBottom(false);
  }, []);

  return {
    containerRef,
    isAtBottom,
    scrollToBottom,
    onViewportEnter,
    onViewportLeave,
  };
}
