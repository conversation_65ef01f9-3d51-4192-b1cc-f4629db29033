'use client';

import Link from '@mui/joy/Link';
import Image from 'next/image';
import React, { useMemo, useState } from 'react';
import { useFormappLocale } from '@bika/contents/i18n';
import { TemplateTags } from '@bika/domains/template/client/template-tags';
import { useUIFrameworkContext } from '@bika/ui/framework/context';
import { trpcClient } from '@toolsdk.ai/domain/client/api-client';
import CodeExampleSection from '@toolsdk.ai/domain/client/components/component-code-section';
import SearchOutlined from '@bika/ui/icons/components/search_outlined';
import { Input } from '@bika/ui/forms';
import { Stack } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/texts';
import type { iString } from '@bika/types/i18n/bo';
import { PackageSimpleVO } from '@toolsdk.ai/sdk-ts/types/vo';
import { PackageVOArrayRenderer } from '@toolsdk.ai/domain/client/packages/package-simple-vo-array.renderer';
import _ from 'lodash';

interface Props {
  toolsCount: number;
  initPackages: PackageSimpleVO[];
  disableCover?: boolean;
  categories: { key: string; name: string; description: string }[];
}

export function HomePageClient(props: Props) {
  const [packages, setPackages] = useState(props.initPackages);
  // const [kind, setKind] = useState(() => props.initKind);
  const [query, setQuery] = useState<string>('');
  const { t } = useFormappLocale();
  const frameworkCtx = useUIFrameworkContext();

  // When the category is not set, the backend will return packages in the 'featured' category by default.
  // Do not specify a default value on the frontend, otherwise it will affect the search results.
  const category = frameworkCtx.searchParams.get('category') || undefined;
  const [loading, setLoading] = useState(false);

  const searchPackages = React.useCallback(
    _.debounce(async (sQuery: string | undefined, sCat: string | undefined) => {
      setLoading(true);
      try {
        // todo pagination
        const res = await trpcClient.packages.search.query({
          query: sQuery,
          // category: sCat,
          // 忽略category，实现全局搜索
          category: sQuery ? undefined : sCat,
          pagination: {
            pageNo: 1,
            pageSize: 100,
          },
        });
        setPackages(res.data);
      } finally {
        setLoading(false);
      }
    }, 300),
    [],
  );

  const categories: { key: string; name: iString; description: iString }[] = useMemo(() => {
    //  合并 props.categories + featured
    return [{ key: 'featured', name: 'Featured', description: '' }, ...props.categories];
  }, [props.categories]);

  return (
    <Stack display="flex" flexDirection="column" alignItems="center" justifyContent="center" px={2}>
      {props.disableCover !== true && (
        <>
          <Typography mt={4} mb={2} fontSize={48} level="h1" textAlign={'center'}>
            {props.toolsCount}+ {t.toolsdk.website.title}
          </Typography>
          <Typography mb={2} textColor="var(--text-secondary)" level="b1" sx={{ textAlign: 'center' }}>
            {t.toolsdk.website.description}
            <Link target="_blank" href="/help/tutorials/getting-start">
              How it works?
            </Link>
          </Typography>
          <Image
            src="/toolsdk/hero.png"
            alt="ToolSDK.ai Hero Photo"
            style={{ marginTop: 5 }}
            width={900}
            height={450}
          />
          <CodeExampleSection />
        </>
      )}

      <Stack my={3}>
        <Input
          sx={{
            width: frameworkCtx.isMobile ? '100%' : '1000px',
            height: frameworkCtx.isMobile ? '48px' : '64px',
            borderRadius: frameworkCtx.isMobile ? '24px' : '64px',
          }}
          startDecorator={<SearchOutlined size={frameworkCtx.isMobile ? 20 : 32} />}
          value={query}
          size="lg"
          placeholder="Search MCP Servers..."
          onChange={async (e) => {
            const newQ = e.target.value.trim();
            setQuery(newQ);
            searchPackages(newQ, category);
          }}
        />
      </Stack>

      <TemplateTags<string>
        data={categories}
        current={category || 'featured'}
        onCategorySelectChange={(_newVal) => {
          frameworkCtx.searchParams.set('category', _newVal);
          searchPackages(query, _newVal);
        }}
      />

      {/* <ComponentInfoVOArrayRenderer value={components} /> */}
      {loading ? <>Loading...</> : <PackageVOArrayRenderer value={packages} />}
    </Stack>
  );
}
